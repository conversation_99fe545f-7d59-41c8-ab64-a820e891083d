<template>
	<view v-if="visible" class="page">
		<view class="top-status-bar" id="topStatusBar" :style="[{height: 44 + 'px'},statusStyle]">
			<view class="slot-view" :style="[{paddingTop: slotMarginTop},statusStyle]">
				<view class="topTab u-px-20 flex justify-between align-center status-content"
					:style="[{width : slotWidth,height:titleHeight}]">
					<view class="tabLeft">
						<view class="imgView" @click="goBack">
							<image src="https://file.36sm.cn/mttttxxs/2025/02/12/c1ae23498e1142499c10fb7c4365b028.png"
								mode=""></image>
						</view>
					</view>
					<view class="tabtitle">
						<text>预约</text>
					</view>
					<view>
					</view>
				</view>
				<slot></slot>
			</view>
		</view>
		<view class="detail_view" v-if="shopDetail">
			<view class="shop_detail">
				<view class="Img_pro" v-if="shopDetail.pictureInfoList.length > 0">
					<view class="sliderView">
						<swiper class="banner" :indicator-dots="false" :autoplay="true" :interval="3000"
							:duration="1000" :circular="true" indicator-color="transparent"
							indicator-active-color="#ffc47c" @change="onSwiperChange">
							<swiper-item v-for="(item, i) in shopDetail.pictureInfoList" :key="i">
								<image :src="item" style="" mode="aspectFill"></image>
							</swiper-item>
						</swiper>
					</view>
				</view>
				<view class="Img_pro" v-else>
					<view class="sliderView">
						<swiper class="banner" :indicator-dots="false" :autoplay="true" :interval="3000"
							:duration="1000" :circular="true" indicator-color="transparent"
							indicator-active-color="#ffc47c" @change="onSwiperChange">
							<swiper-item v-for="(item, i) in detailInfo.itemImgs" :key="i">
								<image :src="item.shopbg" style="" mode="aspectFill"></image>
							</swiper-item>
						</swiper>
					</view>
				</view>
				<view class="toptitle">
					<view class="top">
						<view class="leftimg">
							<image :src="shopDetail.coverImageUrl" mode="aspectFill"
								style="width: 172rpx;height: 172rpx;border-radius: 16rpx;"></image>
						</view>
						<view class="rightdetail">
							<view class="topname">
								<view class="title">
									<text>{{shopDetail.placeName}}</text>
								</view>
								<view class="distance">
									<image src="/static/images/navgo.png" mode=""
										style="width: 28rpx;height: 28rpx;margin-right: 4rpx;"></image>
									<view class="">
										<text>{{shopDetail.jlInfo || '0km'}}</text>
									</view>
								</view>
							</view>
							<view class="center">
								<view class="nearest">
									<text>就近</text>
								</view>
								<view class="phone" @click="callPhone">
									<image src="/static/images/phone.png" mode="" style="width: 48rpx;height: 48rpx;">
									</image>
								</view>
							</view>
							<view class="bottomtime">
								<view class="time">
									<text style="margin-right: 20rpx;">营业时间:</text>
									<text>{{formatTime(shopDetail.extendDt1)}}--{{formatTime(shopDetail.extendDt2)}}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="address">
						<view class="leftaddress" @click="openlocation(shopDetail)">
							<!-- <text class="title">商家地址</text> -->
							<text class="content">{{shopDetail.adDetails}}</text>
							<image src="https://file.36sm.cn/jaxinfo/2025/02/28/0920510eaf034e358723153d96f6c89b.png"
								mode="" style="width: 48rpx;height: 48rpx;"></image>
						</view>
						<view class="handoff" @click="handoff">
							<text>切换</text>
						</view>
					</view>
				</view>
				<view class="arrivaltime">
					<view class="titletop">
						<view class="title">
							<text>到店时间</text>
						</view>
					</view>
					<view class="RangeTime">
						<view class="timeCon">
							<!-- 时间选择器 -->
							<DatePicker :isOpen="isOpenShow" modelValue="yyyy-MM-dd" @bindChange='bindChange'
								isGreaterY="y"></DatePicker>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="bottomCon">
			<view class="topTab">
				<view class="topNav">
					<view class="bottom_left">
						<text class="title">预约时间:</text>
						<text class="timevalue" v-if="selectedDD">
							{{selectedDD.selectedDate}} {{selectedDD.selectedHour}}:{{selectedDD.selectedMinute}}</text>
					</view>
					<view class="customer" v-if="memberId" @click="ChangeCus">客户名称：<text style="color:#FF6900">{{memberName}}</text>
						<text style="margin-left: 15rpx;font-size: 32rpx;">切换</text>
					</view>

				</view>
				<view @click="selectengineer" class="bottom_right" :class="shopDetail.yyStatus == 0 ? 'Proofed' : ''">
					<text v-if="shopDetail.yyStatus == 0">门店未营业</text>
					<text v-else>选择项目</text>
				</view>
			</view>
			<view style="height: 68rpx;">
			</view>
		</view>

		<uni-popup ref="Phonepopup" type="bottom">
			<view class="popupBox">
				<view class="lianxiren">
					<view class="title">
						选择联系人
					</view>
					<view class="lianxiren_item" v-for="(item,index) in shopDetail.contactsInfoList" :key="index"
						@tap="phoneTal(item.phone)">
						{{item.name}}：{{item.phone}}<text v-if="item.role">（{{item.role}}）</text>
					</view>
				</view>
				<u-button :plain="true" text="取消" @tap="handelclose"></u-button>
			</view>
		</uni-popup>

		<uni-popup ref="MoreStore" type="bottom" :is-mask-click="false">
			<view class="popupStore">
				<view class="topView">
					<view class="title">
						<text></text>
					</view>
					<view class="title">
						<text>更多门店</text>
					</view>
					<view class="Close" @click="closeStorePopup">
						<image src="https://file.36sm.cn/jaxinfo/2025/02/15/d1360f78eb22474e8590431e65c47724.png"
							mode="" style="width: 35rpx;height: 35rpx;"></image>
					</view>
				</view>
				<view class="ShopView">
					<scroll-view class="scroll-container" scroll-y="true" @scrolltolower="onscrollBottom"
						v-if="listVisible">
						<view class="SeaProList">
							<z-paging ref="paging" @query="queryList" style="z-index: 1;" :safe-area-inset-bottom="true"
								:use-page-scroll="true" :show-refresher-when-reload="true"
								:auto-show-back-top-top="true" :auto="false">
								<refresh-loading slot="refresher"></refresh-loading>
								<view class="shopList" v-if="shopList.length > 0">
									<view class="shopitem" v-for="(item, index) in shopList" :key="index">
										<view class="shopdetail">
											<view class="itemleft">
												<image :src="item.coverImageUrl" mode="aspectFill"
													style="width: 160rpx;height: 160rpx;border-radius: 16rpx;">
												</image>
											</view>
											<view class="itemright">
												<view class="top">
													<view class="shopname">
														<text>{{item.placeName}}</text>
													</view>
													<view class="distance">
														<image src="/static/images/navgo.png" mode=""
															style="width: 28rpx;height: 28rpx;margin-right: 4rpx;">
														</image>
														<view class="">
															<text>3.2km</text>
														</view>
													</view>
												</view>
												<view class="center">
													<text>10:00-20:00</text>
													<view class="line"></view>
													<image src="/static/images/phone.png" mode=""
														@click="callphone(item)" style="width: 40rpx;height: 40rpx;">
													</image>
												</view>
												<view class="bottom">
													<view class="left">
														<text>{{item.adDetails}}</text>
													</view>
													<view class="right" @click="booking(item)">
														<text>预约</text>
													</view>
												</view>
											</view>
										</view>
									</view>
								</view>
								<view class="noOrder" v-if="shopList.length == 0">
									<image src="https://file.36sm.cn/xtjyjt/images/common/noorder.png" mode="aspectFill"
										style="width: 200rpx;height: 200rpx;"></image>
									<text class="notitle">暂无产品</text>
								</view>
								<component-bottom-loading v-if="loadingStatus"></component-bottom-loading>
								<component-bottom-line propMsg="数据加载完全" :propStatus="bottom_line_status"
									v-if="!loadingStatus && shopList.length > 0"></component-bottom-line>
							</z-paging>
						</view>
					</scroll-view>
					<!-- <cc-gifLoading v-if="!listVisible"
						gifSrc="https://tuanku.oss-cn-hangzhou.aliyuncs.com/2024/11/04/9b4101a6e9e64565adea6f2553702da5.gif"></cc-gifLoading> -->
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="MemberPopup" type="center" :is-mask-click="false">
			<view class="popupMember">
				<view class="topView">
					<view class="title">
						<text></text>
					</view>
					<view class="title">
						<text>开单会员</text>
					</view>
					<view class="Close" @click="closeMemberPopup">
						<image src="https://file.36sm.cn/jaxinfo/2025/02/15/d1360f78eb22474e8590431e65c47724.png"
							mode="" style="width: 40rpx;height: 40rpx;"></image>
					</view>
				</view>
				<view class="SearchView">
					<view style="width: 420rpx;	">
						<u--input type="text" name="phone" v-model="Memparams.phone" maxlength="11"
							placeholder="请输入手机号查询" :custom-style="{height: '32rpx'}" clearable></u--input>
					</view>
					<u-button text="查询" @click="SearchBtN"
						:custom-style="{  backgroundColor: '#FF6900', borderRadius: '10rpx',color: '#fff', width: '160rpx', height: '60rpx'}"
						style="margin-left: 25rpx;" :loading="SearchLoading"></u-button>
				</view>
				<view class="MemberList">
					<view class="MemberView" v-for="(item,index) in memberList" :key="index"
						@click="SelectMember(item,index)">
						<view class="MemberDetail" :class="memberId == item.memberId ? 'isActive' : ''">
							<view class="leftImg">
								<image :src="item.logoUrl" mode="aspectFill" style=""></image>
							</view>
							<view class="userPhone">
								<text>{{item.phone}}</text>
							</view>
							<view class="userName">
								<text>{{item.nickName}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="ConfirmBtn" @click="BtnConfirm">
					<text>确认</text>
				</view>
			</view>
		</uni-popup>
	</view>
	<cc-gifLoading v-else
		gifSrc="https://testksjjkl.oss-cn-hangzhou.aliyuncs.com/txjy/2025/02/12/142c4f60aee441ab884da7052337a6a7.gif"></cc-gifLoading>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex"
	import api from '@/common/request/index.js'
	import DatePicker from '../../components/date_picker/index.vue'
	import componentBottomLine from '../../components/bottom-line/bottom-line';
	import componentBottomLoading from "../../components/bottom-loading/bottom-loading.vue"
	import componentCopyright from '../../components/copyright/copyright';
	export default {
		data() {
			return {
				visible: false,
				slotHeight: 0,
				detailInfo: {
					itemImgs: [{
							shopbg: 'https://file.36sm.cn/mttttxxs/2025/02/11/5a3895dfd84842068f2d127d51b22dd7.png',
						},
						{
							shopbg: 'https://file.36sm.cn/mttttxxs/2025/02/11/5a3895dfd84842068f2d127d51b22dd7.png',
						},
						{
							shopbg: 'https://file.36sm.cn/mttttxxs/2025/02/11/5a3895dfd84842068f2d127d51b22dd7.png',
						}

					],
					shopimg: 'https://file.36sm.cn/mttttxxs/2025/02/11/2e6f59969cdc4460b1b197a42593b526.png',
					title: '杭州西湖文化广场店',
					range: '3.2km',
					phone: '19907022958',
					shoplocation: '杭州拱墅区XXX'
				},
				shopDetail: {},
				currentIndex: 1,
				isactive: true,
				// 打开时间选择器
				isOpenShow: true,
				isCheched: false,
				expireTime: '',
				authState: false,
				placeId: '',
				params: {
					size: 10,
					page: 1
				},
				listVisible: false,
				shopList: [],
				loadingStatus: false,
				bottom_line_status: false,
				selectedDD: {},
				selectable: false,
				adLatitude: uni.getStorageSync('adLatitude'),
				adLongitude: uni.getStorageSync('adLongitude'),
				// 开单选择的用户ID
				memberId: '',
				memberName: '',
				Memparams: {
					page: 1,
					size: 50,
					phone: ''
				},
				memberList: [],
				SearchLoading: false
			}
		},
		components: {
			DatePicker,
			componentBottomLine,
			componentBottomLoading,
			componentCopyright
		},
		props: {
			topOffset: {
				type: Number,
				default: 5
			},
			statusStyle: {
				type: Object,
				default: () => {}
			}
		},
		components: {},
		computed: {
			statusBarHeight() {
				let h = 0
				h = this.slotHeight + this.topOffset
				this.$emit('setStatusBarHeight', h)
				return h
			},
			slotMarginTop() {
				let mt = '5px';
				// #ifdef APP-PLUS
				mt = getApp().globalData.statusBarHeight + 'px'
				// #endif
				// #ifdef MP
				mt = getApp().globalData.menuTop + 'px'
				// #endif
				return mt
			},
			slotWidth() {
				// #ifdef APP-PLUS || H5
				let sw = '100vw';
				// #endif
				// #ifdef MP
				let sw = this.width ? this.width : `${getApp().globalData.menuLeft}px`
				console.log(sw, 'swsw')
				// #endif
				return sw
			},
			titleHeight() {
				let th = "auto"
				// #ifdef MP
				th = getApp().globalData.menuHeight + 'px'
				// #endif
				return th
			},
		},
		mounted() {
			const observerView = {
				top: getApp().globalData.statusBarHeight,
				bottom: getApp().globalData.menuBottom
			}
			this.slotObserver = uni.createIntersectionObserver(this)
			this.slotObserver.relativeTo('.top-status-bar', observerView).observe('.slot-view', node => {
				const {
					top,
					height
				} = node.boundingClientRect
				if (height) {
					this.slotHeight = height
				}
			})
		},
		onLoad(params) {
			console.log(params, '路由参数')
			if (params.adLatitude) {
				this.adLatitude = params.adLatitude
				this.adLongitude = params.adLongitude
			}
			if (params.placeId) {
				this.placeId = params.placeId
				console.log(this.placeId, '店铺详情id')
				this.getShopDetail()
			} else {
				this.getoneShopList()
			}
			this.getMember()
		},
		methods: {
			SearchBtN() {
				if (this.SearchLoading) {
					return
				}
				this.SearchLoading = true
				this.memberId = ''
				this.memberName = ''
				this.getMember()
			},
			getMember() {
				api({
					url: `js/kd/member/get/list?page=${this.Memparams.page}&size=${this.Memparams.size}&phone=${this.Memparams.phone}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data.list
						this.memberList = [...data]
						console.log(this.memberList, '会员列表数据')

					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
					setTimeout(() => {
						this.SearchLoading = false
					}, 400)
				}).catch(err => {
					uni.showToast({
						title: err.msg || '请求超时',
						icon: "none"
					});
				});
			},
			SelectMember(item, index) {
				console.log(item, index, '选择的用户')
				this.memberId = item.memberId
				this.memberName = item.nickName
				console.log(this.memberId, '当前选择的用户')
			},
			ChangeCus() {
				this.$refs.MemberPopup.open()
			},
			BtnConfirm() {
				if (this.memberId == '') {
					uni.showToast({
						title: '您还未选择开单的用户',
						icon: "none"
					});
					return
				}
				this.$refs.MemberPopup.close()
			},
			formatTime(dateString) {
				// 提取时间部分并返回
				const time = dateString.split(' ')[1]; // 获取时间部分
				return time.slice(0, 5); // 截取前五个字符（小时和分钟部分）
			},
			getoneShopList() {
				api({
					url: `tx/place/get/list?page=1&size=1&adLatitude=${this.adLatitude}&adLongitude=${this.adLongitude}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					if (res.code == 200) {
						const data = res.data.data.list
						console.log(data, '相关门店数据')
						this.placeId = data[0].placeId
						this.getShopDetail()
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '请求超时',
						icon: "none"
					});
				});
			},
			onscrollBottom() {
				console.log('下拉触底')
				this.loadingStatus = true
				this.params.page++
				this.getShopList()
			},
			queryList() {
				this.params.page = 1
				this.params.size = 10
				console.log('上拉刷新')
				this.getShopList()
				setTimeout(() => {
					this.$refs.paging.complete(false);
				}, 1200)
			},
			callphone(item) {
				console.log(item, '打电话')
			},
			booking(item) {
				console.log(item, '预约')
				uni.reLaunch({
					url: `/pageg/shop_detail/index?placeId=${item.placeId}`
				});
			},
			handoff() {
				// 切换门店
				console.log('更多门店弹窗')
				this.$refs.MoreStore.open()
				uni.showLoading({
					title: '加载中',
				});
				this.params.page = 1
				this.getShopList()
			},
			closeStorePopup() {
				this.$refs.MoreStore.close()
				// this.listVisible = false
			},
			closeMemberPopup() {
				// this.memberId = ''
				// this.memberName = ''
				this.$refs.MemberPopup.close()
			},
			getShopDetail() {
				const params = {
					adLongitude: this.adLongitude,
					adLatitude: this.adLatitude
				}
				api({
					url: `tx/place/id/info/${this.placeId}`,
					// url: `tx/place/id/info/${this.placeId}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'POST',
					data: params
				}).then(res => {
					setTimeout(() => {
						this.$data.visible = true


					}, 500)
					setTimeout(() => {
						this.$refs.MemberPopup.open()
					}, 1000)
					if (res.code == 200) {
						console.log(res.data.data, '店铺详情数据')
						this.shopDetail = res.data.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				})
			},
			// 拨打电话
			phoneTal(phoneNum) {
				console.log(phoneNum);
				uni.makePhoneCall({
						phoneNumber: phoneNum
					})
					.catch((e) => {
						// console.log (e)
					});
			},
			openlocation(detail) {
				// 打开门店详情
				uni.openLocation({
					latitude: detail.adLatitude,
					longitude: detail.adLongitude,
					scale: 18,
					success() {
						console.log('打开地图成功')
					},
					fail(error) {
						uni.showToast({
							title: '地图打开失败',
							icon: "none"
						});
					}
				})
			},
			callPhone() {
				this.$refs.Phonepopup.open()
			},
			handelclose() {
				this.$refs.Phonepopup.close()
			},
			onTermOfValidity(e) {
				if (this.authState) return
				this.isOpenShow = true
			},
			bindChange(e) {
				// console.log(e, 'eeee');
				let selectedDate = e.selectedDate;
				// 判断selectedDate是否为"今天"
				if (selectedDate === "今天") {
					const today = new Date();
					const month = today.getMonth() + 1; // 获取当前月份（0-11，+1是为了正确显示）
					const day = today.getDate(); // 获取当前日期
					selectedDate = `${month}月${day}日`; // 转换为 "X月X日" 格式
				}
				this.selectedDD = {
					...e,
					selectedDate
				};
				// 你可以在这里执行其他逻辑，如emit事件等
				// console.log(this.selectedDD, 'updated selectedDD');

			},
			onCloseDate(e) {
				this.expireTime = ''
				this.isOpenShow = false
			},
			onBindChage(e, data) {
				// this.expireTime = data.year + '-' + data.month + '-' + data.day
				// this.isOpenShow = false
			},
			// 获取相关门店的列表
			getShopList() {
				api({
					url: `tx/place/get/list?page=${this.params.page}&size=${this.params.size}`,
					header: {
						"jarepair-platform": uni.getStorageSync("token")
					},
					method: 'get'
				}).then(res => {
					setTimeout(() => {
						this.listVisible = true
						uni.hideLoading();
					}, 800)
					if (res.code == 200) {
						const data = res.data.data
						console.log(data, '相关门店数据')
						if (this.shopList.length == data.totalCount) {
							// 数据加载完全
							setTimeout(() => {
								this.loadingStatus = false;
								this.bottom_line_status = true
							})

							if (this.params.page == 1) {
								this.shopList = [...data.list]
							}
						} else {
							if (this.params.page == 1) {
								this.shopList = [...data.list]
							} else {
								this.shopList = [...this.shopList, ...data.list]
							}
							// 确保视图刷新
							this.$set(this, 'shopList', this.shopList);
							setTimeout(() => {
								this.loadingStatus = false;
							}, 500)
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '请求超时',
						icon: "none"
					});
				});
			},
			goBack() {
				uni.navigateBack({
					success: () => {},
					fail: (err) => {
						uni.reLaunch({
							url: "/pages/home/<USER>"
						})
					}
				})
			},
			onSwiperChange(e) {
				// console.log(e, 'eee')
			},
			selectengineer() {
				if (this.shopDetail.yyStatus == 0) {
					uni.showToast({
						title: '门店未营业，请留意营业时间',
						icon: "none"
					});
					return
				}
				if (!this.memberId) {
					// 技师 需要先选择对应的客户 才能开单 
					uni.showToast({
						title: '开单客户还未选择',
						icon: "none"
					});
					this.$refs.MemberPopup.open()
					return
				}

				console.log(this.selectedDD, '预约到店的时间')
				if (this.selectable) return
				this.selectable = true
				// 选择技师页面
				uni.navigateTo({
					url: `/pageg/all_engineers/index?selectedDate=${this.selectedDD.selectedDate}&selectedHour=${this.selectedDD.selectedHour}&selectedMinute=${this.selectedDD.selectedMinute}&placeName=${this.shopDetail.placeName}&placeId=${this.shopDetail.placeId}&jlInfo=${this.shopDetail.jlInfo}&memberId=${this.memberId}`
				})
				setTimeout(() => {
					this.selectable = false
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		// box-sizing: border-box;
		background-color: #FBF5F1;
		width: 100vw;
		height: 100vh;
	}

	.top-status-bar {
		width: 100%;
		position: fixed;
		top: 0rpx;
		z-index: 999;

		// background-color: transparent;
		.slot-view {
			.topTab {
				// padding: 0 50rpx;
				.tabLeft {
					.imgView {
						width: 60rpx;
						height: 60rpx;

						image {
							width: 60rpx;
							height: 60rpx;
						}
					}
				}

				.tabtitle {
					// width: 72rpx;
					height: 44rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 36rpx;
					color: #FFFFFF;
					line-height: 44rpx;
					// text-align: center;
					font-style: normal;
					text-transform: none;
					margin-left: 50rpx;
				}

				.tabRight {
					display: flex;

					.imgView {
						width: 60rpx;
						height: 60rpx;
						margin-left: 30rpx;

						image {
							width: 60rpx;
							height: 60rpx;
						}
					}
				}
			}
		}
	}

	.detail_view {
		.shop_detail {
			width: 100%;

			.Img_pro {
				z-index: 9;
				position: relative;

				.sliderView {
					width: 100%;
					margin: 0 auto;
					height: 450rpx;
					position: relative;

					.banner {
						width: 100% !important;
						height: 450rpx !important;

						image {
							width: 100% !important;
							height: 450rpx !important;
						}
					}

					.tabBtn {
						position: absolute;
						bottom: 5%;
						left: 50%;
						transform: translate(-50%, 0);
						background: rgba(228, 228, 228, 0.5);
						color: #000;
						border-radius: 20rpx;
						z-index: 1;

						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 22rpx;
						color: #333333;
						height: 40rpx;
						line-height: 40rpx;
						font-style: normal;
						text-transform: none;
						display: flex;
						align-items: center;

						.imgbtn {
							border-radius: 20rpx;
							padding: 0 10rpx;
							min-width: 140rpx;
							text-align: center;
						}

						.videobtn {
							border-radius: 20rpx;
							padding: 0 10rpx;
							min-width: 100rpx;
							text-align: center;
						}

						.isavtive {
							background-color: #fff;
						}
					}

				}
			}

			.toptitle {
				z-index: 99;
				position: relative;
				margin: 0 auto;
				margin-top: -80rpx;
				width: 710rpx;
				height: 260rpx;
				background: #FFFFFF;
				border-radius: 16rpx;

				.top {
					margin: 0 24rpx;
					display: flex;
					align-items: center;

					.leftimg {
						margin-top: -18rpx;
					}

					.rightdetail {
						margin-left: 16rpx;
						width: 480rpx;
						height: 150rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-around;

						.topname {
							flex: 1;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.title {
								font-family: PingFang SC, PingFang SC;
								font-weight: bold;
								font-size: 32rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;
							}

							.distance {
								display: flex;
								align-items: center;

								text {
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #666666;
									text-align: center;
									font-style: normal;
									text-transform: none;
								}
							}
						}

						.center {
							flex: 1;
							display: flex;
							justify-content: space-between;
							align-items: center;

							// margin-top: 10rpx;
							.nearest {
								width: 84rpx;
								height: 32rpx;
								line-height: 32rpx;
								border-radius: 24rpx;
								border: 2rpx solid #FF6900;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 26rpx;
								color: #FF6900;
								text-align: center;
								font-style: normal;
								text-transform: none;
							}
						}

						.bottomtime {
							flex: 1;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.time {
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 30rpx;
								color: #666666;
								font-style: normal;
								text-transform: none;
							}
						}
					}
				}

				.address {
					width: 662rpx;
					height: 60rpx;
					line-height: 60rpx;
					margin: 0 auto;
					margin-top: 18rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.leftaddress {
						display: flex;
						align-items: center;
						// .title {
						// 	font-family: PingFang SC, PingFang SC;
						// 	font-weight: 600;
						// 	font-size: 30rpx;
						// 	color: #666666;
						// 	text-align: left;
						// 	font-style: normal;
						// 	text-transform: none;
						// }

						.content {
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 26rpx;
							color: #666666;
							font-style: normal;
							text-transform: none;
							margin-right: 10rpx;
							width: 450rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.handoff {
						width: 136rpx;
						height: 52rpx;
						line-height: 52rpx;
						background: #FF6900;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						font-style: normal;
						color: #fff;
						text-transform: none;
					}
				}

			}

			.arrivaltime {
				width: 710rpx;
				height: 724rpx;
				border-radius: 16rpx;
				background: #FFFFFF;
				margin: 0 auto;
				margin-top: 16rpx;

				.titletop {
					width: 100%;
					height: 100rpx;
					line-height: 100rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-size: 30rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
					letter-spacing: 1rpx;
					border-top-left-radius: 16rpx;
					border-top-right-radius: 16rpx;

					.title {
						padding: 0 24rpx;
					}
				}

				.discount {
					width: 632rpx;
					margin: 0 auto;
					display: flex;
					justify-content: space-between;

					.leftdis,
					.rightdis {
						width: 264rpx;
						height: 128rpx;
						background: #FFF4ED;
						border-radius: 16rpx;
						display: flex;
						// justify-content: space-around;
						align-items: center;
						padding: 0 22rpx;

						.leftz {
							width: 105rpx;
							height: 44rpx;
							background: #FFF4ED;
							border-right: 1rpx solid #FF6900;
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 36rpx;
							color: #FF6900;
							// text-align: center;
							font-style: normal;
							text-transform: none;
							margin-right: 16rpx;
						}

						.rightz {
							font-family: PingFang SC, PingFang SC;
							font-weight: 600;
							font-size: 28rpx;
							color: #FF6900;
							font-style: normal;
							text-transform: none;
						}
					}

					.active {}
				}

				.RangeTime {
					width: 710rpx;
					// height: 496rpx;
					height: 620rpx;
					border-radius: 0rpx 0rpx 16rpx 16rpx;

					.timeCon {
						padding: 0 43rpx;
					}
				}
			}
		}
	}

	.bottomCon {
		position: fixed;
		width: 100vw;
		height: 188rpx;
		background: #FFFFFF;
		bottom: 0;

		.topTab {
			height: 120rpx;
			line-height: 120rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.topNav{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.bottom_left {
					margin-left: 20rpx;
					display: flex;
					align-items: center;
					height: 40rpx;
					line-height: 40rpx;
					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						font-style: normal;
						text-transform: none;
						margin-right: 10rpx;
					}
				
					.timevalue {
						font-family: PingFang SC, PingFang SC;
						font-weight: 600;
						font-size: 32rpx;
						color: #FF6900;
						line-height: 60rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}
				.customer{
					margin-top: 10rpx;
					height: 40rpx;
					line-height: 40rpx;
					// background-color: skyblue;
				}
			}


			.bottom_right {
				margin-right: 20rpx;
				width: 240rpx;
				height: 80rpx;
				line-height: 80rpx;
				background: #FF6900;
				border-radius: 94rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.Proofed {
				background: #ccc;
			}
		}
	}

	.popupBox {
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;
		padding-bottom: 80rpx;
		margin-bottom: -80rpx;

		.lianxiren {
			width: 100%;
			min-height: 0rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: center;

			.title {
				height: 100rpx;
				line-height: 100rpx;
				font-size: 34rpx;
				font-weight: bold;
			}

			.lianxiren_item {
				width: 100%;
				height: 80rpx;
				background-color: #f5f5f5;
				text-align: center;
				line-height: 80rpx;
				margin-bottom: 5rpx;
			}

		}
	}

	.popupStore {
		width: 100%;
		height: 1000rpx;
		// min-height: 500rpx;
		// max-height: 1000rpx;
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;
		padding-bottom: 80rpx;
		margin-bottom: -80rpx;
		padding: 40rpx 0;

		.topView {
			width: 90%;
			margin: 0 auto;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 50rpx;
			font-family: PingFang SC, PingFang SC;
			font-size: 30rpx;
			color: #333333;
			font-weight: 600;
			font-style: normal;
			text-transform: none;
			letter-spacing: 1rpx;
		}

		.ShopView {
			.scroll-container {
				margin-top: 10rpx;
				height: calc(970rpx);
				overflow-y: auto;

				.SeaProList {
					.shopList {
						margin: 0 auto;
						width: 96%;
						background-color: #fff;
						padding: 24rpx 0 4rpx 0;
						border-radius: 24rpx;
						margin-top: 16rpx;

						.shopitem {
							margin: 0 24rpx;

							.shopdetail {
								margin-bottom: 20rpx;
								height: 180rpx;
								// border-bottom: 1rpx solid #E2DED4;
								display: flex;
								align-items: flex-start;

								.itemleft {}

								.itemright {
									margin-left: 24rpx;
									height: 150rpx;
									flex: 1;
									display: flex;
									flex-direction: column;
									justify-content: space-between;

									.top {
										display: flex;
										justify-content: space-between;

										.shopname {
											font-family: PingFang SC, PingFang SC;
											font-size: 30rpx;
											color: #000;
											font-weight: 600;
											// text-align: center;
											font-style: normal;
											text-transform: none;
											width: 340rpx;
											overflow: hidden;
											white-space: nowrap;
											text-overflow: ellipsis;
										}

										.distance {
											display: flex;
											align-items: center;
											font-family: PingFang SC, PingFang SC;
											font-weight: 500;
											font-size: 28rpx;
											color: #333333;
											text-align: center;
											font-style: normal;
											text-transform: none;
										}
									}

									.center {
										display: flex;
										align-items: center;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 26rpx;
										color: #909090;
										font-style: normal;
										text-transform: none;

										.line {
											width: 4rpx;
											height: 16rpx;
											background: #E2DED4;
											border-radius: 0rpx 0rpx 0rpx 0rpx;
											margin: 0 8rpx;
										}
									}

									.bottom {
										display: flex;
										justify-content: space-between;
										align-items: center;

										.left {
											font-family: PingFang SC, PingFang SC;
											font-weight: 400;
											font-size: 28rpx;
											letter-spacing: 1rpx;
											color: #909090;
											font-style: normal;
											text-transform: none;
											width: 340rpx;
											overflow: hidden;
											white-space: nowrap;
											text-overflow: ellipsis;
										}

										.right {
											width: 120rpx;
											height: 52rpx;
											line-height: 52rpx;
											text-align: center;
											background: #FF6900;
											border-radius: 8rpx;
											color: #fff;
											font-family: PingFang SC, PingFang SC;
											font-weight: 500;
											font-size: 28rpx;
											font-style: normal;
											text-transform: none;
										}
									}
								}
							}
						}
					}

					.noOrder {
						height: 1000rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.notitle {
							margin-top: 50rpx;
						}
					}
				}

			}
		}
	}

	.popupMember {
		width: 650rpx;
		height: 960rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 20rpx 0;

		.topView {
			width: 94%;
			margin: 0 auto;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 50rpx;
			font-family: PingFang SC, PingFang SC;
			font-size: 30rpx;
			color: #333333;
			font-weight: 600;
			font-style: normal;
			text-transform: none;
			letter-spacing: 1rpx;
		}

		.SearchView {
			height: 60rpx;
			width: 94%;
			margin: 20rpx auto;
			display: flex;
			align-items: center;
		}

		.MemberList {
			width: 94%;
			margin: 0 auto;
			margin-bottom: 30rpx;
			height: 715rpx;
			overflow-y: auto;

			.MemberView {
				padding: 0 20rpx;

				.MemberDetail {
					display: flex;
					align-items: center;
					margin: 10rpx 0;
					justify-content: space-between;
					height: 100%;

					.leftImg {
						width: 90rpx;
						height: 92rpx;
						border-radius: 50%;
					}

					image {
						border-radius: 50%;
						width: 100%;
						height: 100%;
					}
				}

				.isActive {
					border: 1rpx solid #ff6900;
				}
			}
		}

		.ConfirmBtn {
			width: 420rpx;
			height: 60rpx;
			margin: 0 auto;
			line-height: 60rpx;
			background: #FF6900;
			border-radius: 94rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
	}
</style>